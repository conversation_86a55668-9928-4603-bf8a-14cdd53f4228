# 專案結構說明 (Source Tree Documentation)

**專案**: Outlook Summary System - 半導體郵件處理系統  
**版本**: 3.1 (Flask 藍圖架構完成版本)  
**最後更新**: 2025-08-11

## 📋 目錄

- [整體架構概覽](#整體架構概覽)
- [前端目錄結構](#前端目錄結構)
- [後端目錄結構](#後端目錄結構)
- [模組間依賴關係](#模組間依賴關係)
- [檔案命名規範](#檔案命名規範)
- [新功能開發指南](#新功能開發指南)

---

## 🏗️ 整體架構概覽

```
D:\project\python\outlook_summary\
├── 📁 frontend/                      # 前端主目錄 (Flask + HTML/JS) ✅ 已完成
├── 📁 src/                           # 核心原始碼目錄
├── 📁 docs/                          # 專案文檔 ✅ 已完成
├── 📁 tests/                         # 測試目錄
├── 📁 .bmad-core/                    # BMad 核心配置 ✅
├── 📁 .kiro/                         # Kiro 專案管理配置 ✅
├── 📁 .claude/                       # Claude Agents 配置 ✅
├── 📄 start_integrated_services.py   # 統一服務啟動程式 ✅
├── 📄 requirements.txt               # Python 依賴 ✅
├── 📄 project_info.json              # 專案統計資訊 ✅
└── 📄 README.md                      # 專案說明 ✅ 最新
```

### 架構設計原則

1. **領域驅動設計 (DDD)**: 按業務功能劃分模組
2. **關注點分離**: 前端、後端、測試、文檔分離
3. **模組化架構**: 每個功能模組獨立開發和部署
4. **漸進式重構**: 保持向後相容，逐步現代化

---

## 🎨 前端目錄結構

### 📁 frontend/ - 前端主目錄

```
frontend/ ✅ 已完成模組化重構
├── 📄 app.py                         # Flask 主應用 (工廠模式) ✅
├── 📄 config.py                      # 統一配置管理 ✅
├── 📄 __init__.py                    # 前端模組初始化 ✅
├── 📁 shared/                        # 共享前端資源 ✅
├── 📁 email/                         # 郵件模組 ✅
├── 📁 analytics/                     # 分析模組 ✅
├── 📁 file_management/               # 檔案管理模組 ✅
├── 📁 eqc/                           # EQC模組 ✅
├── 📁 tasks/                         # 任務模組 ✅
├── 📁 monitoring/                    # 監控模組 ✅
└── 📁 logs/                          # 日誌檔案 ✅
```

#### Flask 主應用結構

```python
# frontend/app.py - 應用工廠模式
def create_app(config_name='development'):
    app = Flask(__name__)
    app.config.from_object(config[config_name])
    
    # 註冊藍圖
    from .email.routes.email_routes import email_bp
    from .analytics.routes.analytics_routes import analytics_bp
    # ... 其他模組
    
    app.register_blueprint(email_bp, url_prefix='/email')
    app.register_blueprint(analytics_bp, url_prefix='/analytics')
    
    return app
```

### 📧 功能模組結構 (以 email 為例)

```
frontend/email/
├── 📁 templates/                     # HTML 模板
│   ├── 📄 inbox.html                # 收件匣頁面
│   ├── 📄 email_detail.html         # 郵件詳情頁面
│   ├── 📄 email_compose.html        # 撰寫郵件頁面
│   └── 📄 email_settings.html       # 郵件設定頁面
├── 📁 static/                        # 靜態資源
│   ├── 📁 css/
│   │   └── 📄 email.css             # 郵件專用樣式
│   ├── 📁 js/
│   │   ├── 📄 email-list.js         # 郵件列表邏輯
│   │   ├── 📄 email-detail.js       # 郵件詳情邏輯
│   │   └── 📄 email-api.js          # 郵件 API 調用
│   └── 📁 images/                   # 模組專用圖片
├── 📁 components/                    # 可重用 HTML 組件
│   ├── 📄 email-card.html           # 郵件卡片組件
│   └── 📄 attachment-viewer.html    # 附件查看器
├── 📁 routes/                        # Flask 路由處理
│   └── 📄 email_routes.py           # 郵件相關路由
└── 📋 README.md                      # 模組說明文檔
```

#### 模組職責說明

| 模組 | 職責 | 主要功能 |
|------|------|----------|
| **email** | 郵件管理 | 收件匣、郵件詳情、附件處理 |
| **analytics** | 分析統計 | 數據分析、報表生成、圖表展示 |
| **file-management** | 檔案管理 | 檔案上傳、附件瀏覽、檔案監控 |
| **eqc** | 品質控制 | EQC 檢查、合規驗證、品質報告 |
| **tasks** | 任務管理 | 任務調度、並發處理、任務監控 |
| **monitoring** | 系統監控 | 健康檢查、效能監控、系統狀態 |

### 📁 shared/ - 共享資源

```
frontend/shared/
├── 📁 templates/                     # 共享模板
│   ├── 📄 base.html                 # 基礎模板
│   ├── 📄 layout.html               # 主要佈局
│   └── 📁 components/               # 共享組件
│       ├── 📄 navbar.html           # 導航列
│       ├── 📄 sidebar.html          # 側邊欄
│       ├── 📄 modal.html            # 模態框
│       └── 📄 loading.html          # 載入動畫
├── 📁 static/                        # 共享靜態資源
│   ├── 📁 css/
│   │   ├── 📄 global.css            # 全域樣式
│   │   ├── 📄 components.css        # 共享組件樣式
│   │   └── 📄 theme.css             # 主題樣式
│   ├── 📁 js/
│   │   ├── 📄 common.js             # 共用 JavaScript
│   │   ├── 📄 api-client.js         # 統一 API 客戶端
│   │   ├── 📄 utils.js              # 工具函數
│   │   └── 📄 constants.js          # 前端常數
│   ├── 📁 lib/                      # 第三方函式庫
│   │   ├── 📄 jquery.min.js
│   │   ├── 📄 bootstrap.min.js
│   │   └── 📄 chart.min.js
│   └── 📁 images/                   # 共享圖片
└── 📁 utils/                         # Python 工具模組
    ├── 📄 __init__.py
    ├── 📄 error_handler.py          # 全域錯誤處理器
    └── 📄 api_response.py           # 統一 API 回應格式
```

---

## 🖥️ 後端目錄結構

### 📁 backend/ - 後端主目錄

```
src/ ✅ 核心系統架構
├── 📁 application/                   # 應用層 - 使用案例
├── 📁 domain/                        # 領域層 - 業務邏輯
├── 📁 infrastructure/                # 基礎設施層 - 外部整合
├── 📁 presentation/                  # 展示層 - API & Web
├── 📁 shared/                        # 共享元件
├── 📄 main.py                        # 主入口點
└── 📄 start_integrated_services.py   # 統一服務啟動
```

### 📧 後端服務結構 (以 email 為例)

```
backend/email/
├── 📁 api/                           # API 路由
│   ├── 📄 routes.py                 # FastAPI 路由定義
│   ├── 📄 schemas.py                # Pydantic 模式
│   └── 📄 dependencies.py           # 依賴注入
├── 📁 services/                      # 業務邏輯
│   ├── 📄 outlook_service.py        # Outlook 整合服務
│   ├── 📄 pop3_service.py           # POP3 郵件服務
│   ├── 📄 email_parser.py           # 郵件解析服務
│   └── 📄 sync_service.py           # 郵件同步服務
├── 📁 models/                        # 數據模型
│   ├── 📄 email.py                  # 郵件數據模型
│   └── 📄 attachment.py             # 附件模型
├── 📁 repositories/                  # 數據訪問層
│   └── 📄 email_repository.py       # 郵件數據訪問
├── 📁 core/                          # 核心配置
│   ├── 📄 config.py                 # 服務特定配置
│   └── 📄 exceptions.py             # 自定義異常
└── 📋 README.md                      # 服務說明
```

### 📁 shared/ - 後端共享資源

```
backend/shared/
├── 📁 database/                      # 數據庫相關
│   ├── 📄 base.py                   # 數據庫基礎配置
│   ├── 📄 email_database.py         # 郵件數據庫
│   └── 📄 models.py                 # 共享數據模型
├── 📁 utils/                         # 工具函數
│   ├── 📄 logger.py                 # 統一日誌服務
│   ├── 📄 config_manager.py         # 配置管理器
│   └── 📄 helpers.py                # 輔助函數
├── 📁 middleware/                    # 中介軟體
│   ├── 📄 auth_middleware.py        # 認證中介軟體
│   └── 📄 cors_middleware.py        # CORS 處理
├── 📁 exceptions/                    # 共享異常
│   └── 📄 base_exceptions.py        # 基礎異常類
└── 📁 constants/                     # 後端常數
    └── 📄 app_constants.py          # 應用常數
```

---

## 📚 其他重要目錄

### 📁 docs/ - 文檔目錄

```
docs/
├── 📋 README.md                      # 文檔索引
├── 📁 architecture/                  # 架構文檔
│   ├── 📄 coding-standards.md       # 編碼標準
│   ├── 📄 tech-stack.md             # 技術堆疊
│   └── 📄 source-tree.md            # 專案結構 (本文檔)
├── 📁 migration/                     # 遷移文檔
│   ├── 📄 file-mapping.md           # 檔案映射表
│   ├── 📄 branch-protection-setup.md
│   └── 📄 task-completion-log.md
├── 📁 api/                           # API 文檔
└── 📁 development/                   # 開發指南
```

### 📁 tests/ - 測試目錄

```
tests/
├── 📁 frontend/                      # 前端測試
│   ├── 📁 email/
│   ├── 📁 analytics/
│   └── 📁 shared/
├── 📁 backend/                       # 後端測試
│   ├── 📁 email/
│   ├── 📁 analytics/
│   └── 📁 shared/
├── 📁 integration/                   # 整合測試
└── 📁 e2e/                           # 端到端測試
```

---

## 🔗 模組間依賴關係

### 依賴層級架構

```mermaid
graph TD
    A[前端模組] --> B[shared/前端共享資源]
    C[後端服務] --> D[shared/後端共享資源]
    A --> E[統一 API 介面]
    E --> C
    F[數據庫層] --> G[SQLite]
    C --> F
    H[外部服務] --> I[Outlook API]
    H --> J[POP3 服務]
    C --> H
```

### 模組通訊方式

1. **前端模組間**: 通過 shared 資源和事件系統
2. **前端到後端**: 統一 API 介面 (REST + WebSocket)
3. **後端服務間**: 直接函數調用或消息隊列
4. **數據訪問**: 通過 Repository 模式統一管理

### 依賴規則

- ✅ **允許**: 模組 → shared 資源
- ✅ **允許**: 上層 → 下層 (如 API → Service → Repository)
- ❌ **禁止**: 模組間直接相互依賴
- ❌ **禁止**: 下層 → 上層 (如 Repository → Service)

---

## 📝 檔案命名規範

### Python 檔案

| 類型 | 格式 | 範例 |
|------|------|------|
| 模組檔案 | snake_case.py | email_service.py, data_validator.py |
| 類別檔案 | snake_case.py | email_processor.py, user_manager.py |
| 配置檔案 | config.py | config.py, settings.py |
| 測試檔案 | test_*.py | test_email_service.py |

### JavaScript 檔案

| 類型 | 格式 | 範例 |
|------|------|------|
| 模組檔案 | kebab-case.js | email-manager.js, data-validator.js |
| 類別檔案 | PascalCase.js | EmailManager.js, DataValidator.js |
| 工具檔案 | camelCase.js | apiClient.js, utils.js |

### HTML 模板

| 類型 | 格式 | 範例 |
|------|------|------|
| 頁面模板 | snake_case.html | email_detail.html, user_profile.html |
| 組件模板 | kebab-case.html | email-card.html, user-avatar.html |
| 佈局模板 | layout.html | base.html, layout.html |

### CSS 檔案

| 類型 | 格式 | 範例 |
|------|------|------|
| 模組樣式 | module.css | email.css, analytics.css |
| 組件樣式 | component.css | components.css, widgets.css |
| 全域樣式 | global.css | global.css, theme.css |

---

## 🚀 新功能開發指南

### 1. 評估功能歸屬

```bash
# 問題檢查清單
1. 這個功能屬於哪個業務領域？
   - 郵件相關 → email 模組
   - 數據分析 → analytics 模組
   - 檔案處理 → file-management 模組
   - 品質控制 → eqc 模組
   - 任務處理 → tasks 模組
   - 系統監控 → monitoring 模組

2. 是否需要新建模組？
   - 如果是全新業務領域，考慮新建模組
   - 如果是現有功能擴展，在對應模組內開發

3. 是否需要共享資源？
   - 多模組使用 → 放入 shared 目錄
   - 單模組使用 → 放入模組內部
```

### 2. 前端功能開發流程

```bash
# Step 1: 建立分支
git checkout -b feature/new-email-filter

# Step 2: 建立檔案結構
frontend/email/
├── templates/email_filter.html          # 新頁面
├── static/js/email-filter.js            # 新功能邏輯
├── static/css/email-filter.css          # 新功能樣式
└── routes/email_routes.py               # 添加路由

# Step 3: 實作功能
# 3.1 添加路由
@email_bp.route('/filter')
def email_filter():
    return render_template('email/email_filter.html')

# 3.2 建立模板 (使用共享基礎模板)
{% extends "shared/base.html" %}

# 3.3 實作 JavaScript 邏輯
class EmailFilter {
    // 功能實作
}

# 3.4 添加樣式
.email-filter { /* 樣式定義 */ }

# Step 4: 測試
pytest tests/frontend/email/test_email_filter.py

# Step 5: 更新文檔
# 更新 frontend/email/README.md
```

### 3. 後端功能開發流程

```bash
# Step 1: 建立分支
git checkout -b feature/email-sentiment-analysis

# Step 2: 建立檔案結構
backend/email/
├── api/routes.py                        # 添加新 API 端點
├── services/sentiment_service.py       # 新業務邏輯服務
├── models/sentiment.py                 # 新數據模型
└── repositories/sentiment_repository.py # 新數據訪問

# Step 3: 實作功能
# 3.1 定義數據模型
class EmailSentiment(BaseModel):
    email_id: str
    sentiment_score: float
    sentiment_label: str

# 3.2 實作業務邏輯
class SentimentService:
    async def analyze_email_sentiment(self, email_content: str):
        # 分析邏輯

# 3.3 添加 API 端點
@router.post("/analyze-sentiment")
async def analyze_sentiment(email_id: str):
    # API 實作

# 3.4 實作數據訪問
class SentimentRepository:
    def save_sentiment(self, sentiment: EmailSentiment):
        # 保存邏輯

# Step 4: 測試
pytest tests/backend/email/test_sentiment_service.py

# Step 5: 更新文檔
# 更新 backend/email/README.md 和 API 文檔
```

### 4. 跨模組功能開發

```bash
# 當功能涉及多個模組時
# 例: 添加郵件統計到分析模組

# Step 1: 識別依賴關係
email 模組 (數據來源) → analytics 模組 (數據展示)

# Step 2: 設計 API 介面
# 在 email 模組添加統計 API
GET /api/email/statistics

# Step 3: 在 analytics 模組使用數據
# analytics 模組調用 email 模組的 API

# Step 4: 確保解耦
# 不直接調用其他模組的內部函數
# 通過 API 或事件系統通訊
```

---

## 🔧 開發環境配置

### 目錄權限和環境

```bash
# 開發環境結構
development/
├── frontend/          # 前端開發伺服器 (Flask dev server)
├── backend/           # 後端 API 服務 (FastAPI/Flask)
├── database/          # SQLite 數據庫檔案
└── logs/              # 應用日誌檔案

# 檔案權限
frontend/static/       # 靜態資源 (只讀)
backend/data/          # 數據檔案 (讀寫)
logs/                  # 日誌目錄 (寫入)
```

### IDE 配置建議

```json
// .vscode/settings.json
{
    "python.defaultInterpreterPath": "./venv/bin/python",
    "python.linting.enabled": true,
    "python.linting.flake8Enabled": true,
    "python.formatting.provider": "black",
    "files.associations": {
        "*.html": "jinja-html"
    },
    "emmet.includeLanguages": {
        "jinja-html": "html"
    }
}
```

---

## 📊 效能考量

### 檔案組織對效能的影響

1. **靜態資源優化**: 按模組分離，減少不必要的資源載入
2. **模板繼承**: 使用共享基礎模板，減少重複代碼
3. **JavaScript 模組化**: 按需載入，避免全域污染
4. **CSS 分層**: 全域 → 組件 → 模組，優化載入順序

### 建議的載入策略

```html
<!-- 基礎模板載入順序 -->
<head>
    <!-- 1. 全域 CSS (必要) -->
    <link rel="stylesheet" href="{{ url_for('shared.static', filename='css/global.css') }}">
    
    <!-- 2. 組件 CSS (必要) -->
    <link rel="stylesheet" href="{{ url_for('shared.static', filename='css/components.css') }}">
    
    <!-- 3. 模組特定 CSS (按需) -->
    {% block module_css %}{% endblock %}
</head>

<body>
    <!-- 頁面內容 -->
    
    <!-- 4. 基礎 JavaScript (必要) -->
    <script src="{{ url_for('shared.static', filename='js/common.js') }}"></script>
    
    <!-- 5. 模組特定 JavaScript (按需) -->
    {% block module_js %}{% endblock %}
</body>
```

---

## 🎯 最佳實踐總結

### 模組設計原則

1. **單一職責**: 每個模組只負責一個業務領域
2. **高內聚低耦合**: 模組內部緊密相關，模組間依賴最小
3. **介面隔離**: 通過明確定義的 API 進行模組間通訊
4. **開閉原則**: 對擴展開放，對修改封閉

### 檔案組織最佳實踐

1. **按功能分組**: 相關檔案放在同一目錄
2. **層次清晰**: 避免過深的目錄嵌套 (≤ 4 層)
3. **命名一致**: 使用統一的命名規範
4. **文檔完整**: 每個模組都有詳細的 README

### 開發流程建議

1. **功能設計**: 先設計 API 介面，再實作細節
2. **測試驅動**: 先寫測試，後寫實作
3. **增量開發**: 小步快跑，頻繁整合
4. **文檔同步**: 程式碼變更同時更新文檔

---

**文檔維護**: 本結構說明將隨專案發展持續更新，請關注最新版本的變更記錄。

**特注**: 本文檔反映了 task/3-migrate-files 分支的最新狀態，前端 Flask 藍圖架構重構已 100% 完成。