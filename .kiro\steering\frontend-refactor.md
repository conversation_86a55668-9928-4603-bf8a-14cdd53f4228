---
inclusion: fileMatch
filePatterns:
  - ".kiro/specs/vue-frontend-migration/**"
  - "frontend/**"
  - "email_inbox_app.py"
---

# Frontend Refactoring Guidelines

## 🎉 已完成的重構工作 (2025-01-08)

### ✅ 任務 2: Flask 主應用程式重構 (已完成)

**重構成果:**
- **模組化架構**: 成功將單一 Flask 應用程式重構為模組化藍圖系統
- **配置管理**: 建立統一的配置管理系統，支援開發/測試/生產環境
- **錯誤處理**: 實作全域錯誤處理機制，統一處理 Web 和 API 錯誤
- **向後相容**: 所有現有 API 端點和路由保持不變
- **可擴展性**: 為未來 Vue.js 遷移建立清晰的模組邊界

**已建立的檔案結構:**
```
frontend/
├── app.py                    # Flask 主應用程式 (工廠模式)
├── config.py                 # 統一配置管理
├── __init__.py               # 前端模組初始化
├── shared/
│   ├── __init__.py
│   └── utils/
│       ├── __init__.py
│       └── error_handler.py  # 全域錯誤處理器
└── [module]/routes/          # 各模組路由系統
    ├── email_routes.py       # 郵件功能 (完整遷移)
    ├── analytics_routes.py   # 分析統計 (基礎架構)
    ├── file_routes.py        # 檔案管理 (基礎架構)
    ├── eqc_routes.py         # EQC 功能 (基礎架構)
    ├── task_routes.py        # 任務管理 (基礎架構)
    └── monitoring_routes.py  # 監控功能 (基礎架構)
```

**技術特點:**
- **藍圖系統**: 每個功能模組獨立藍圖，URL 前綴隔離
- **配置靈活**: 支援多環境配置切換
- **錯誤統一**: 統一的錯誤處理和回應格式
- **模組邊界**: 為 Vue.js 遷移建立清晰邊界

## Refactoring Principles

### MVP Approach
- **MUST maintain existing functionality** - No feature changes during restructuring
- **MUST preserve all current URLs** - Ensure backward compatibility
- **MUST keep same API endpoints** - Backend services remain unchanged
- **MUST maintain database connections** - No data migration required

### File Movement Strategy
- **Use `git mv` command** - Preserve file history
- **Move then delete** - Never keep duplicate files
- **Update imports immediately** - Fix all path references after moving
- **Test after each move** - Verify functionality before proceeding

### Module Organization Rules
- **One concern per module** - Clear separation of responsibilities
- **Shared resources in shared/** - Common templates, CSS, JS in shared module
- **Module independence** - Minimize cross-module dependencies
- **Consistent naming** - Use snake_case for Python, kebab-case for URLs

## Directory Structure Standards

### Frontend Module Structure
Each module MUST follow this structure:
```
frontend/{module}/
├── templates/          # HTML templates for this module
├── static/
│   ├── css/           # Module-specific styles
│   ├── js/            # Module-specific JavaScript
│   └── images/        # Module-specific images
├── components/        # Reusable HTML components
├── routes/           # Flask route definitions
└── README.md         # Module documentation
```

### Import Path Updates
When moving files, update imports following this pattern:
```python
# OLD
from email_inbox_app import some_function

# NEW
from frontend.email.routes.email_routes import some_function
```

### Template Path Updates
Update template references in Flask routes:
```python
# OLD
return render_template('inbox.html')

# NEW
return render_template('email/templates/inbox.html')
```

### Static Asset Path Updates
Update static asset references in templates:
```html
<!-- OLD -->
<link rel="stylesheet" href="{{ url_for('static', filename='css/email.css') }}">

<!-- NEW -->
<link rel="stylesheet" href="{{ url_for('email.static', filename='css/email.css') }}">
```

## Quality Assurance

### Testing Requirements
- **Test after each file move** - Verify page loads correctly
- **Check all links** - Ensure no 404 errors
- **Validate database connections** - Confirm data access works
- **Cross-browser testing** - Test in Chrome, Firefox, Edge

### Documentation Requirements
- **Update file mapping table** - Record every file movement
- **Update module README** - Document module structure and purpose
- **Commit message format** - Use standardized format for tracking

### Rollback Procedures
- **Git branch isolation** - Work in feature branches
- **Incremental commits** - Small, focused commits for easy rollback
- **Backup verification** - Ensure Git history is preserved
- **Quick rollback plan** - Document steps to revert changes

## Common Pitfalls to Avoid

### File Movement Mistakes
- **DON'T copy files** - Always move, never duplicate
- **DON'T change logic** - Only move files and update paths
- **DON'T skip testing** - Test every change immediately
- **DON'T batch too many changes** - Keep commits small and focused

### Import Path Mistakes
- **DON'T use relative imports** - Always use absolute imports from project root
- **DON'T forget __init__.py files** - Ensure Python packages are properly defined
- **DON'T mix old and new paths** - Update all references consistently

### Template and Static Asset Mistakes
- **DON'T hardcode paths** - Use Flask's url_for() function
- **DON'T forget to register blueprints** - Ensure Flask can find static files
- **DON'T break CSS/JS references** - Update all asset paths in templates

## Success Criteria

### Functional Requirements
- All existing pages load without errors
- All existing functionality works as before
- No broken links or missing assets
- Database connections remain stable

### Technical Requirements
- Clean Git history with proper commit messages
- Complete file mapping documentation
- All tests pass
- No duplicate files in repository

### Team Requirements
- All team members can run the application locally
- Development environment setup is documented
- Code review process is followed
- Knowledge transfer is complete