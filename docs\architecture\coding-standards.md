# 編碼標準 (Coding Standards)

**專案**: Outlook Summary System - 半導體郵件處理系統  
**版本**: 3.1 (Flask 藍圖架構完成版本)  
**最後更新**: 2025-08-11

## 📋 目錄

- [Python 編碼標準](#python-編碼標準)
- [JavaScript 編碼標準](#javascript-編碼標準)
- [HTML/CSS 標準](#htmlcss-標準)
- [Flask/FastAPI 架構標準](#flaskfastapi-架構標準)
- [測試標準](#測試標準)
- [Git 提交標準](#git-提交標準)
- [安全編碼標準](#安全編碼標準)

---

## 🐍 Python 編碼標準

### 基本規範 (PEP 8)

```python
# ✅ 正確示例
class EmailProcessor:
    """郵件處理器類別"""
    
    def __init__(self, config: dict) -> None:
        self.config = config
        self._processed_count = 0
    
    def process_email(self, email_data: dict) -> bool:
        """處理單封郵件
        
        Args:
            email_data: 郵件數據字典
            
        Returns:
            bool: 處理成功返回True
            
        Raises:
            EmailProcessingError: 處理失敗時拋出
        """
        try:
            # 處理邏輯
            return True
        except Exception as e:
            logger.error(f"郵件處理失敗: {e}")
            raise EmailProcessingError(str(e))

# ❌ 錯誤示例
class emailprocessor:
    def __init__(self,config):
        self.config=config
    
    def processEmail(self,emailData):
        # 處理郵件
        pass
```

### 命名規範

| 類型 | 規範 | 範例 |
|------|------|------|
| 類別 | PascalCase | `EmailProcessor`, `DataValidator` |
| 函數/方法 | snake_case | `process_email()`, `validate_data()` |
| 變數 | snake_case | `email_count`, `is_valid` |
| 常數 | UPPER_SNAKE_CASE | `MAX_RETRY_COUNT`, `API_TIMEOUT` |
| 私有屬性 | _snake_case | `_processed_count`, `_config` |
| 模組 | snake_case | `email_processor.py`, `data_validator.py` |

### 類型註解

```python
from typing import List, Dict, Optional, Union
from dataclasses import dataclass

@dataclass
class EmailMessage:
    subject: str
    sender: str
    recipients: List[str]
    content: str
    attachments: Optional[List[str]] = None

def filter_emails(
    emails: List[EmailMessage], 
    criteria: Dict[str, Union[str, int]]
) -> List[EmailMessage]:
    """過濾郵件列表"""
    return [email for email in emails if meets_criteria(email, criteria)]
```

### 異常處理

```python
class EmailProcessingError(Exception):
    """郵件處理自定義異常"""
    pass

def process_email_batch(emails: List[dict]) -> Dict[str, int]:
    """批量處理郵件"""
    results = {"success": 0, "failed": 0}
    
    for email in emails:
        try:
            process_single_email(email)
            results["success"] += 1
        except EmailProcessingError as e:
            logger.warning(f"處理郵件失敗 {email.get('id', 'unknown')}: {e}")
            results["failed"] += 1
        except Exception as e:
            logger.error(f"未預期錯誤: {e}")
            results["failed"] += 1
    
    return results
```

---

## 🌐 JavaScript 編碼標準

### ES6+ 語法

```javascript
// ✅ 正確示例 - 使用 ES6 類別和模組
class EmailManager {
    constructor(config = {}) {
        this.config = { timeout: 5000, ...config };
        this.emailList = [];
    }
    
    async fetchEmails() {
        try {
            const response = await fetch('/api/email/list');
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.message || '獲取郵件失敗');
            }
            
            this.emailList = data.emails || [];
            return this.emailList;
        } catch (error) {
            console.error('郵件獲取錯誤:', error);
            this.showErrorMessage(error.message);
            throw error;
        }
    }
    
    showErrorMessage(message) {
        const notification = document.createElement('div');
        notification.className = 'alert alert-danger';
        notification.textContent = message;
        document.body.appendChild(notification);
        
        setTimeout(() => notification.remove(), 5000);
    }
}

// 匯出模組
export default EmailManager;
export { EmailManager };

// ❌ 錯誤示例
function EmailManager(config) {
    var self = this;
    this.config = config;
    this.fetchEmails = function() {
        $.get('/api/email/list', function(data) {
            // 處理回應
        }).fail(function() {
            alert('錯誤');
        });
    };
}
```

### 命名規範

| 類型 | 規範 | 範例 |
|------|------|------|
| 類別 | PascalCase | `EmailManager`, `DataValidator` |
| 函數/方法 | camelCase | `fetchEmails()`, `validateForm()` |
| 變數 | camelCase | `emailCount`, `isValid` |
| 常數 | UPPER_SNAKE_CASE | `API_TIMEOUT`, `MAX_RETRIES` |
| 檔案 | kebab-case | `email-manager.js`, `data-validator.js` |

### 錯誤處理

```javascript
class ApiClient {
    static async request(url, options = {}) {
        const defaultOptions = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        };
        
        try {
            const response = await fetch(url, { ...defaultOptions, ...options });
            const data = await response.json();
            
            if (!response.ok) {
                throw new ApiError(data.message, response.status, data);
            }
            
            return data;
        } catch (error) {
            if (error instanceof ApiError) {
                throw error;
            }
            throw new ApiError('網路錯誤', 0, { originalError: error });
        }
    }
}

class ApiError extends Error {
    constructor(message, status = 0, details = {}) {
        super(message);
        this.name = 'ApiError';
        this.status = status;
        this.details = details;
    }
}
```

---

## 🎨 HTML/CSS 標準

### 語意化 HTML

```html
<!-- ✅ 正確示例 -->
<main class="email-container">
    <header class="email-header">
        <h1 class="email-title">收件匣</h1>
        <nav class="email-nav">
            <button class="btn btn-primary" data-action="refresh">重新整理</button>
        </nav>
    </header>
    
    <section class="email-list" role="list">
        <article class="email-item" role="listitem" data-email-id="123">
            <h2 class="email-subject">郵件主旨</h2>
            <p class="email-sender">寄件者: <EMAIL></p>
            <time class="email-date" datetime="2025-08-11T14:30:00Z">
                2025/08/11 14:30
            </time>
        </article>
    </section>
</main>

<!-- ❌ 錯誤示例 -->
<div class="container">
    <div class="title">收件匣</div>
    <div class="list">
        <div class="item">
            <div>郵件主旨</div>
            <div>寄件者</div>
        </div>
    </div>
</div>
```

### CSS 組織

```css
/* ✅ 正確示例 - 模組化 CSS */

/* 基礎變數 */
:root {
    --primary-color: #007bff;
    --danger-color: #dc3545;
    --border-radius: 0.375rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
}

/* 組件樣式 */
.email-item {
    padding: var(--spacing-md);
    border: 1px solid #dee2e6;
    border-radius: var(--border-radius);
    margin-bottom: var(--spacing-sm);
    transition: box-shadow 0.15s ease-in-out;
}

.email-item:hover {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.email-item--unread {
    background-color: #f8f9fa;
    font-weight: 600;
}

/* 響應式設計 */
@media (max-width: 768px) {
    .email-item {
        padding: var(--spacing-sm);
    }
}
```

---

## 🌶️ Flask/FastAPI 架構標準

### Flask 藍圖組織

```python
# frontend/email/routes/email_routes.py
from flask import Blueprint, render_template, request, jsonify
from frontend.shared.utils.error_handler import handle_api_error
from frontend.shared.utils.api_response import ApiResponse

email_bp = Blueprint(
    'email', 
    __name__, 
    template_folder='../templates',
    static_folder='../static',
    static_url_path='/static/email'
)

@email_bp.route('/')
@email_bp.route('/inbox')
def inbox():
    """郵件收件匣頁面"""
    try:
        return render_template('email/inbox.html')
    except Exception as e:
        return handle_api_error(e)

@email_bp.route('/api/list')
def api_email_list():
    """獲取郵件列表API"""
    try:
        page = request.args.get('page', 1, type=int)
        size = request.args.get('size', 20, type=int)
        
        # 業務邏輯
        emails = get_email_list(page, size)
        total = get_email_count()
        
        return jsonify(ApiResponse.paginated(emails, page, size, total))
    except Exception as e:
        return jsonify(ApiResponse.error(str(e))), 500
```

### FastAPI 服務組織

```python
# backend/email/api/routes.py
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from typing import List, Optional

router = APIRouter(prefix="/email", tags=["email"])

class EmailResponse(BaseModel):
    id: str
    subject: str
    sender: str
    is_read: bool

@router.get("/list", response_model=List[EmailResponse])
async def get_emails(
    page: int = 1,
    size: int = 20,
    email_service: EmailService = Depends(get_email_service)
):
    """獲取郵件列表"""
    try:
        emails = await email_service.get_emails(page, size)
        return [EmailResponse(**email.dict()) for email in emails]
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
```

---

## 🧪 測試標準

### Pytest 測試結構

```python
# tests/test_email_service.py
import pytest
from unittest.mock import Mock, patch
from frontend.email.routes.email_routes import email_bp
from frontend.shared.utils.error_handler import handle_api_error

class TestEmailService:
    """郵件服務測試類別"""
    
    @pytest.fixture
    def email_service(self):
        """測試用的郵件服務實例"""
        return EmailService()
    
    @pytest.fixture
    def sample_email_data(self):
        """範例郵件數據"""
        return {
            "subject": "測試郵件",
            "sender": "<EMAIL>",
            "content": "測試內容"
        }
    
    def test_create_email_success(self, email_service, sample_email_data):
        """測試創建郵件成功"""
        # Arrange
        expected_email = Email(**sample_email_data)
        
        # Act
        result = email_service.create_email(sample_email_data)
        
        # Assert
        assert result.subject == expected_email.subject
        assert result.sender == expected_email.sender
        assert result.content == expected_email.content
    
    def test_create_email_missing_subject_fails(self, email_service):
        """測試缺少主旨時創建郵件失敗"""
        # Arrange
        invalid_data = {"sender": "<EMAIL>"}
        
        # Act & Assert
        with pytest.raises(ValueError, match="郵件主旨不能為空"):
            email_service.create_email(invalid_data)
    
    @patch('backend.email.services.email_service.database')
    def test_save_email_database_error(self, mock_db, email_service, sample_email_data):
        """測試數據庫錯誤處理"""
        # Arrange
        mock_db.save.side_effect = Exception("數據庫錯誤")
        
        # Act & Assert
        with pytest.raises(Exception, match="數據庫錯誤"):
            email_service.create_email(sample_email_data)
```

### 測試覆蓋率要求

```bash
# 最低覆蓋率: 80%
pytest --cov=frontend --cov=src --cov-report=html --cov-report=term
```

---

## 📝 Git 提交標準

### 提交訊息格式

```bash
# 格式: type(scope): description
# 
# 詳細說明 (可選)
# 
# 相關問題: #123

# 範例:
feat(email): 新增郵件批量處理功能

新增批量處理郵件的API端點和前端界面：
- 支援多選郵件操作  
- 批量標記為已讀/未讀
- 批量刪除功能
- 整合 Dramatiq 異步處理

相關任務: task/3-migrate-files

# 類型說明:
# feat: 新功能
# fix: 錯誤修復
# docs: 文檔更新
# style: 代碼格式調整
# refactor: 重構
# test: 測試相關
# chore: 工具/配置變更
```

### 分支命名

```bash
# 功能分支
feature/email-batch-processing
feature/vue-migration

# 錯誤修復分支
fix/email-parsing-error
hotfix/security-vulnerability

# 任務分支
task/1-create-structure
task/2-refactor-app
```

---

## 🔒 安全編碼標準

### 輸入驗證

```python
from flask import request
from pydantic import BaseModel, validator
import bleach

class EmailInput(BaseModel):
    subject: str
    content: str
    
    @validator('subject')
    def validate_subject(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError('郵件主旨不能為空')
        if len(v) > 200:
            raise ValueError('郵件主旨過長')
        return bleach.clean(v.strip())
    
    @validator('content')
    def validate_content(cls, v):
        # XSS 防護
        return bleach.clean(v, tags=['p', 'br', 'strong', 'em'])

@email_bp.route('/api/create', methods=['POST'])
def create_email():
    try:
        # 驗證輸入
        email_data = EmailInput(**request.json)
        
        # 處理邏輯
        result = email_service.create_email(email_data.dict())
        return jsonify(ApiResponse.success(result))
    except ValidationError as e:
        return jsonify(ApiResponse.error("輸入驗證失敗", details=e.errors())), 400
```

### SQL 注入防護

```python
# ✅ 正確示例 - 使用 SQLAlchemy ORM
def get_emails_by_sender(sender_email: str):
    return session.query(Email).filter(Email.sender == sender_email).all()

# ✅ 正確示例 - 使用參數化查詢
def get_emails_raw_sql(sender_email: str):
    query = "SELECT * FROM emails WHERE sender = %s"
    return session.execute(query, (sender_email,))

# ❌ 錯誤示例 - 直接字符串拼接
def get_emails_unsafe(sender_email: str):
    query = f"SELECT * FROM emails WHERE sender = '{sender_email}'"
    return session.execute(query)
```

---

## 📊 程式碼品質檢查

### 自動化檢查工具

```yaml
# .pre-commit-config.yaml
repos:
  - repo: https://github.com/psf/black
    rev: 22.3.0
    hooks:
      - id: black
        language_version: python3
  
  - repo: https://github.com/pycqa/flake8
    rev: 4.0.1
    hooks:
      - id: flake8
        args: [--max-line-length=88]
  
  - repo: https://github.com/pre-commit/mirrors-eslint
    rev: v8.15.0
    hooks:
      - id: eslint
        files: \.(js|ts|jsx|tsx)$
        types: [file]
```

### 品質指標

| 指標 | 目標值 | 工具 |
|------|--------|------|
| 測試覆蓋率 | ≥ 80% | pytest-cov |
| 程式碼重複率 | ≤ 3% | radon |
| 循環複雜度 | ≤ 10 | flake8-complexity |
| 型別覆蓋率 | ≥ 90% | mypy |

---

## 📚 最佳實踐總結

### 開發流程

1. **編碼前**: 閱讀相關模組的現有代碼，理解架構模式
2. **開發中**: 遵循命名規範，添加適當註解，處理異常
3. **測試**: 編寫單元測試，確保覆蓋率要求
4. **提交前**: 運行 linter 和測試，檢查無誤後提交
5. **程式碼審查**: 關注安全性、效能、可維護性

### 團隊協作

- **統一工具**: 使用相同的 IDE 配置和格式化工具
- **文檔更新**: 代碼變更時同步更新相關文檔
- **知識分享**: 定期分享最佳實踐和常見問題解決方案

---

**文檔維護**: 本編碼標準將隨團隊實踐不斷更新完善，請定期檢查最新版本。