# Frontend - 前端開發指南

## 概述

本目錄包含半導體郵件處理系統的前端代碼，採用模組化架構設計，為未來遷移到 Vue.js 做準備。

## 目錄結構

```
frontend/
├── email/                    # 郵件功能模組
├── analytics/                # 分析統計功能模組
├── file-management/          # 檔案管理功能模組
├── eqc/                      # EQC功能模組
├── tasks/                    # 任務管理功能模組
├── monitoring/               # 監控功能模組
├── shared/                   # 共享前端資源
├── app.py                    # Flask 主應用程式
├── config.py                 # Flask 配置
└── README.md                 # 本檔案
```

## 功能模組

### 六大功能領域

1. **Email (郵件)** - 郵件收件匣管理、郵件詳情查看、郵件設定
2. **Analytics (分析統計)** - 統計儀表板、報表生成、廠商分析
3. **File Management (檔案管理)** - 檔案上傳、檔案瀏覽、附件管理
4. **EQC (設備品質控制)** - EQC儀表板、品質檢查、合規檢查
5. **Tasks (任務管理)** - 任務儀表板、任務隊列、任務調度
6. **Monitoring (監控)** - 系統監控、健康檢查、效能指標

### 共享資源

- **Templates** - 基礎模板、佈局組件、共享組件
- **Static Assets** - 全域樣式、共用JavaScript、第三方函式庫
- **Utilities** - 工具函數、API客戶端、常數定義

## 開發指南

### 模組結構

每個功能模組都遵循相同的目錄結構：

```
module_name/
├── templates/                # HTML 模板
├── static/                   # 靜態資源
│   ├── css/                  # 模組專用樣式
│   ├── js/                   # 模組專用JavaScript
│   └── images/               # 模組專用圖片
├── components/               # 可重用組件
├── routes/                   # 路由處理
└── README.md                 # 模組說明
```

### 技術堆疊

- **Flask 2.3.3** - Web 框架
- **Jinja2** - 模板引擎
- **HTML5 + CSS3 + JavaScript ES6** - 前端技術
- **Bootstrap** - CSS 框架
- **Chart.js** - 圖表庫
- **jQuery** - DOM 操作

## 開發環境設定

1. 確保 Python 虛擬環境已啟動
2. 安裝必要的依賴套件
3. 配置 Flask 應用程式
4. 啟動開發伺服器

## 注意事項

- 本階段重構使用現有技術 (Flask + HTML/JS)
- 保持所有現有功能不變
- 為未來 Vue.js 遷移建立清晰的模組邊界
- 遵循 hexagonal architecture 原則